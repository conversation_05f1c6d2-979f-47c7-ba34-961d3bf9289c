# Installation Guide - Maze Bank Robbery (ESX & OX Compatible)

## Quick Setup

1. **Copy the resource** to your `resources` folder
2. **Add to server.cfg**:
   ```
   ensure r-mazerobbery
   ```
3. **Restart your server**

The resource will automatically detect your framework (ESX or OX) and configure itself accordingly.

## Framework-Specific Setup

### ESX Framework

#### Required Dependencies:
- `es_extended` (ESX Legacy)

#### Optional Dependencies (for enhanced features):
- `esx_hacking` - For better hacking minigames
- `esx_thermite` - For thermite minigames  
- `progressBars` - For progress bar animations

#### Items Setup:
Add these items to your ESX database if they don't exist:

```sql
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('thermite', 'Thermite', 1, 0, 1),
('drill', 'Drill', 5, 0, 1),
('adv_electronics_kit', 'Advanced Electronics Kit', 2, 0, 1),
('purple_dongle', 'Purple Dongle', 1, 1, 1),
('crypto_voucher', 'Crypto Voucher', 0, 0, 1),
('moneyroll', 'Money Roll', 0, 0, 1),
('moneyband', 'Money Band', 0, 0, 1),
('valuegoods', 'Valuable Goods', 1, 0, 1),
('moneybag', 'Money Bag', 2, 0, 1);
```

### OX Framework

#### Required Dependencies:
- `ox_core`
- `ox_inventory`
- `ox_lib`

#### Items Setup:
Add these items to your `ox_inventory/data/items.lua`:

```lua
['thermite'] = {
    label = 'Thermite',
    weight = 1000,
    stack = true,
    close = true,
},
['drill'] = {
    label = 'Drill',
    weight = 5000,
    stack = false,
    close = true,
},
['adv_electronics_kit'] = {
    label = 'Advanced Electronics Kit',
    weight = 2000,
    stack = true,
    close = true,
},
['purple_dongle'] = {
    label = 'Purple Dongle',
    weight = 100,
    stack = true,
    close = true,
},
['crypto_voucher'] = {
    label = 'Crypto Voucher',
    weight = 0,
    stack = true,
    close = true,
},
['moneyroll'] = {
    label = 'Money Roll',
    weight = 0,
    stack = true,
    close = true,
},
['moneyband'] = {
    label = 'Money Band',
    weight = 0,
    stack = true,
    close = true,
},
['valuegoods'] = {
    label = 'Valuable Goods',
    weight = 1000,
    stack = true,
    close = true,
},
['moneybag'] = {
    label = 'Money Bag',
    weight = 2000,
    stack = false,
    close = true,
}
```

## Configuration

### Customizing Item Names
Edit `shared/framework_config.lua` to match your server's item names:

```lua
-- For ESX
FrameworkConfig.ESX.Items = {
    thermite = "your_thermite_item_name",
    drill = "your_drill_item_name",
    -- ... etc
}

-- For OX
FrameworkConfig.OX.Items = {
    thermite = "your_thermite_item_name",
    drill = "your_drill_item_name",
    -- ... etc
}
```

### Customizing Job Names
Edit `shared/framework_config.lua` to match your server's job/group names:

```lua
-- For ESX
FrameworkConfig.ESX.Jobs = {
    police = "your_police_job_name"
}

-- For OX
FrameworkConfig.OX.Groups = {
    police = "your_police_group_name"
}
```

## Troubleshooting

### "No supported framework detected"
- Ensure ESX or OX Core is started before this resource
- Check resource names match exactly (`es_extended` or `ox_core`)
- Verify framework is properly installed and working

### Items not working
- Check item names in your database/inventory config
- Verify items exist in your framework's item system
- Check console for inventory-related errors

### Job permissions not working
- Verify job/group names in configuration
- Check that police players are on duty
- Ensure job system is working properly

### Minigames not working
- Install optional minigame resources for better experience
- Check that exports are available
- Fallback system will use simple RNG if minigames unavailable

## Support

This resource is designed to work out-of-the-box with both ESX and OX frameworks. If you encounter issues:

1. Check the console for error messages
2. Verify all dependencies are installed
3. Ensure item and job names match your server configuration
4. Test with a fresh server restart

The resource includes fallback systems to ensure basic functionality even if optional dependencies are missing.
