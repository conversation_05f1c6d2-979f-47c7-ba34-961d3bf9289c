-- Framework Compatibility Layer for ESX and OX
FrameworkCompat = {}

-- Initialize framework compatibility
function FrameworkCompat.Init()
    if Framework.Name == 'ESX' then
        FrameworkCompat.InitESX()
    elseif Framework.Name == 'OX' then
        FrameworkCompat.InitOX()
    else
        print("^1[ERROR] No supported framework detected! Please install ESX or OX Core.^0")
    end
end

-- ESX Compatibility Functions
function FrameworkCompat.InitESX()
    if IsDuplicityVersion() then -- Server side
        FrameworkCompat.GetPlayer = function(source)
            return Framework.Object.GetPlayerFromId(source)
        end
        
        FrameworkCompat.GetPlayerData = function(source)
            local xPlayer = Framework.Object.GetPlayerFromId(source)
            if xPlayer then
                return {
                    identifier = xPlayer.identifier,
                    job = xPlayer.job.name,
                    jobGrade = xPlayer.job.grade,
                    onDuty = xPlayer.job.name == 'police' and Player(source).state.onDuty or false,
                    firstName = xPlayer.get('firstName') or 'Unknown',
                    lastName = xPlayer.get('lastName') or 'Unknown',
                    sid = xPlayer.identifier
                }
            end
            return nil
        end
        
        FrameworkCompat.HasItem = function(source, item, count)
            local xPlayer = Framework.Object.GetPlayerFromId(source)
            if xPlayer then
                local itemCount = xPlayer.getInventoryItem(item).count
                return itemCount >= (count or 1)
            end
            return false
        end
        
        FrameworkCompat.RemoveItem = function(source, item, count)
            local xPlayer = Framework.Object.GetPlayerFromId(source)
            if xPlayer then
                xPlayer.removeInventoryItem(item, count or 1)
                return true
            end
            return false
        end
        
        FrameworkCompat.AddItem = function(source, item, count, metadata)
            local xPlayer = Framework.Object.GetPlayerFromId(source)
            if xPlayer then
                xPlayer.addInventoryItem(item, count or 1)
                return true
            end
            return false
        end
        
        FrameworkCompat.ShowNotification = function(source, message, type, duration)
            TriggerClientEvent('esx:showNotification', source, message)
        end
        
    else -- Client side
        FrameworkCompat.GetPlayerData = function()
            return Framework.Object.GetPlayerData()
        end
        
        FrameworkCompat.ShowNotification = function(message, type, duration)
            Framework.Object.ShowNotification(message)
        end
    end
end

-- OX Compatibility Functions  
function FrameworkCompat.InitOX()
    if IsDuplicityVersion() then -- Server side
        FrameworkCompat.GetPlayer = function(source)
            return exports.ox_core:GetPlayer(source)
        end
        
        FrameworkCompat.GetPlayerData = function(source)
            local player = exports.ox_core:GetPlayer(source)
            if player then
                return {
                    identifier = player.stateId,
                    job = player.getGroup(),
                    jobGrade = player.getGroupGrade(),
                    onDuty = player.getGroup() == 'police' and Player(source).state.onDuty or false,
                    firstName = player.get('firstName') or 'Unknown',
                    lastName = player.get('lastName') or 'Unknown',
                    sid = player.stateId
                }
            end
            return nil
        end
        
        FrameworkCompat.HasItem = function(source, item, count)
            local player = exports.ox_core:GetPlayer(source)
            if player then
                return exports.ox_inventory:GetItemCount(source, item) >= (count or 1)
            end
            return false
        end
        
        FrameworkCompat.RemoveItem = function(source, item, count)
            return exports.ox_inventory:RemoveItem(source, item, count or 1)
        end
        
        FrameworkCompat.AddItem = function(source, item, count, metadata)
            return exports.ox_inventory:AddItem(source, item, count or 1, metadata)
        end
        
        FrameworkCompat.ShowNotification = function(source, message, type, duration)
            TriggerClientEvent('ox_lib:notify', source, {
                title = 'Maze Bank',
                description = message,
                type = type or 'inform',
                duration = duration or 5000
            })
        end
        
    else -- Client side
        FrameworkCompat.GetPlayerData = function()
            return exports.ox_core:GetPlayerData()
        end
        
        FrameworkCompat.ShowNotification = function(message, type, duration)
            exports.ox_lib:notify({
                title = 'Maze Bank',
                description = message,
                type = type or 'inform',
                duration = duration or 5000
            })
        end
    end
end

-- Unified Progress Bar Function
FrameworkCompat.ShowProgressBar = function(data, callback)
    if Framework.Name == 'ESX' then
        if exports.progressBars then
            exports.progressBars:startUI(data.duration, data.label)
            Citizen.SetTimeout(data.duration, function()
                callback(false) -- false means completed
            end)
        else
            -- Fallback for ESX without progressBars
            Citizen.SetTimeout(data.duration, function()
                callback(false)
            end)
        end
    elseif Framework.Name == 'OX' then
        if exports.ox_lib then
            exports.ox_lib:progressBar({
                duration = data.duration,
                label = data.label,
                useWhileDead = data.useWhileDead or false,
                canCancel = data.canCancel or true,
                disable = data.controlDisables or {},
                anim = data.animation
            })
            callback(false)
        else
            -- Fallback
            Citizen.SetTimeout(data.duration, function()
                callback(false)
            end)
        end
    end
end

-- Unified Callback System
FrameworkCompat.TriggerCallback = function(name, source, cb, ...)
    if Framework.Name == 'ESX' then
        if IsDuplicityVersion() then
            TriggerClientEvent('esx:triggerServerCallback', source, name, ...)
        else
            Framework.Object.TriggerServerCallback(name, cb, ...)
        end
    elseif Framework.Name == 'OX' then
        if IsDuplicityVersion() then
            -- OX server callback
            cb(...)
        else
            -- OX client callback
            exports.ox_core:callback(name, false, cb, ...)
        end
    end
end

-- Initialize on resource start
Citizen.CreateThread(function()
    Citizen.Wait(1000) -- Wait for frameworks to load
    FrameworkCompat.Init()
end)
