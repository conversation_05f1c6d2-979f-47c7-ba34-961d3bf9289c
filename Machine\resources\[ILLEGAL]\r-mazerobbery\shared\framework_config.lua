-- Framework-specific configuration
FrameworkConfig = {}

-- ESX Configuration
FrameworkConfig.ESX = {
    -- Item names (adjust these to match your ESX item database)
    Items = {
        thermite = "thermite",
        drill = "drill",
        electronics_kit = "adv_electronics_kit",
        purple_dongle = "purple_dongle",
        crypto_voucher = "crypto_voucher",
        moneyroll = "moneyroll",
        moneyband = "moneyband",
        valuegoods = "valuegoods",
        moneybag = "moneybag"
    },
    
    -- Job names (adjust these to match your ESX job names)
    Jobs = {
        police = "police"
    },
    
    -- Notification settings
    Notifications = {
        position = "top-right",
        duration = 5000
    },
    
    -- Minigame settings
    Minigames = {
        hacking = {
            resource = "esx_hacking", -- Set to false to disable
            fallback_success_rate = 70
        },
        thermite = {
            resource = "esx_thermite", -- Set to false to disable
            fallback_success_rate = 60
        },
        drilling = {
            fallback_success_rate = 80
        },
        pc_hacking = {
            fallback_success_rate = 75
        }
    }
}

-- OX Configuration
FrameworkConfig.OX = {
    -- Item names (adjust these to match your OX inventory items)
    Items = {
        thermite = "thermite",
        drill = "drill",
        electronics_kit = "adv_electronics_kit",
        purple_dongle = "purple_dongle",
        crypto_voucher = "crypto_voucher",
        moneyroll = "moneyroll",
        moneyband = "moneyband",
        valuegoods = "valuegoods",
        moneybag = "moneybag"
    },
    
    -- Group names (adjust these to match your OX group names)
    Groups = {
        police = "police"
    },
    
    -- Notification settings
    Notifications = {
        position = "top-right",
        duration = 5000
    },
    
    -- Minigame settings
    Minigames = {
        hacking = {
            skill_checks = {'easy', 'easy', 'medium'},
            keys = {'w', 'a', 's', 'd'},
            fallback_success_rate = 70
        },
        thermite = {
            skill_checks = {'easy', 'medium', 'hard'},
            keys = {'w', 'a', 's', 'd'},
            fallback_success_rate = 60
        },
        drilling = {
            fallback_success_rate = 80
        },
        pc_hacking = {
            skill_checks = {'easy', 'medium', 'medium'},
            keys = {'w', 'a', 's', 'd'},
            fallback_success_rate = 75
        }
    }
}

-- Get current framework config
function FrameworkConfig.Get()
    if Framework.Name == 'ESX' then
        return FrameworkConfig.ESX
    elseif Framework.Name == 'OX' then
        return FrameworkConfig.OX
    else
        -- Return ESX config as fallback
        return FrameworkConfig.ESX
    end
end

-- Get item name for current framework
function FrameworkConfig.GetItem(itemKey)
    local config = FrameworkConfig.Get()
    return config.Items[itemKey] or itemKey
end

-- Get job name for current framework
function FrameworkConfig.GetJob(jobKey)
    local config = FrameworkConfig.Get()
    if Framework.Name == 'ESX' then
        return config.Jobs[jobKey] or jobKey
    elseif Framework.Name == 'OX' then
        return config.Groups[jobKey] or jobKey
    else
        return jobKey
    end
end

-- Get minigame config for current framework
function FrameworkConfig.GetMinigame(gameType)
    local config = FrameworkConfig.Get()
    return config.Minigames[gameType] or {}
end
