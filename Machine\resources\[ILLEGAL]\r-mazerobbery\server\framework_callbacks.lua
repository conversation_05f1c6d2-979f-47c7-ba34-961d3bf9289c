-- Framework-compatible callback handlers for Maze Bank Robbery

-- Thermite Handler
local function HandleThermite(source, data, cb)
    local playerData = FrameworkCompat.GetPlayerData(source)
    if playerData then
        if (not GlobalState["AntiShitlord"] or os.time() > GlobalState["AntiShitlord"] or GlobalState["MazeBankInProgress"]) 
           and not GlobalState["MazeBank:Secured"] then
            
            -- Check server restrictions
            if GetGameTimer() < MAZEBANK_SERVER_START_WAIT or (GlobalState["RestartLockdown"] and not GlobalState["MazeBankInProgress"]) then
                FrameworkCompat.ShowNotification(source, "You Notice The Door Is Barricaded For A Storm, Maybe Check Back Later", "error")
                return
            elseif (GlobalState["Duty:police"] or 0) < MAZEBANK_REQUIRED_POLICE and not GlobalState["MazeBankInProgress"] then
                FrameworkCompat.ShowNotification(source, "Enhanced Security Measures Enabled, Maybe Check Back Later When Things Feel Safer", "error")
                return
            elseif GlobalState["RobberiesDisabled"] then
                FrameworkCompat.ShowNotification(source, "Temporarily Disabled, Please See City Announcements", "error")
                return
            elseif GlobalState[string.format("MazeBank:Power:%s", data.boxId)] ~= nil and GlobalState[string.format("MazeBank:Power:%s", data.boxId)] > os.time() then
                FrameworkCompat.ShowNotification(source, "Electric Box Already Disabled", "error")
                return
            end

            local myPos = GetEntityCoords(GetPlayerPed(source))
            
            -- Find thermite point
            for k, v in pairs(_mbElectric) do
                if v.data.boxId == data.boxId and v.isThermite then
                    if #(vector3(data.thermitePoint.coords.x, data.thermitePoint.coords.y, data.thermitePoint.coords.z) - myPos) <= 3.5 then
                        if not _mbInUse.powerBoxes[data.boxId] then
                            _mbInUse.powerBoxes[data.boxId] = source
                            GlobalState["MazeBankInProgress"] = true

                            if FrameworkCompat.HasItem(source, "thermite", 1) then
                                if FrameworkCompat.RemoveItem(source, "thermite", 1) then
                                    print(string.format("[ROBBERY] %s %s (%s) Started Thermiting Maze Bank Power Box %s",
                                        playerData.firstName, playerData.lastName, playerData.sid, data.boxId))
                                    
                                    -- Trigger thermite minigame
                                    TriggerClientEvent("Robbery:Client:StartThermiteGame", source, {
                                        config = { difficulty = 6, time = 10000 },
                                        data = data,
                                    })
                                    
                                    -- Register one-time event for thermite result
                                    local thermiteEventName = "Robbery:Server:ThermiteResult:" .. source .. ":" .. data.boxId
                                    RegisterNetEvent(thermiteEventName, function(success)
                                        if success then
                                            print(string.format("[ROBBERY] %s %s (%s) Successfully Thermited Maze Bank Power Box %s",
                                                playerData.firstName, playerData.lastName, playerData.sid, data.boxId))
                                            
                                            if not GlobalState["AntiShitlord"] or os.time() >= GlobalState["AntiShitlord"] then
                                                GlobalState["AntiShitlord"] = os.time() + (60 * math.random(10, 15))
                                            end

                                            _mbGlobalReset = os.time() + MAZEBANK_RESET_TIME
                                            GlobalState[string.format("MazeBank:Power:%s", data.boxId)] = _mbGlobalReset
                                            
                                            -- Trigger effects and alerts
                                            TriggerEvent("Particles:Server:DoFx", data.thermitePoint.coords, "thermite")
                                            if IsMBPowerDisabled() then
                                                Doors:SetLock("mazebank_offices", false)
                                                CCTV.State.Group:Offline("mazebank")
                                                GlobalState["Fleeca:Disable:mazebank_baycity"] = true
                                            end
                                        end
                                        
                                        _mbInUse.powerBoxes[data.boxId] = false
                                        RemoveEventHandler(thermiteEventName)
                                    end)
                                else
                                    _mbInUse.powerBoxes[data.boxId] = false
                                end
                            else
                                _mbInUse.powerBoxes[data.boxId] = false
                                FrameworkCompat.ShowNotification(source, "You don't have thermite", "error")
                            end
                        else
                            FrameworkCompat.ShowNotification(source, "Someone Is Already Interacting With This", "error")
                        end
                    end
                    break
                end
            end
        else
            FrameworkCompat.ShowNotification(source, "Temporary Emergency Systems Enabled, Check Back In A Bit", "error")
        end
    end
end

-- Drill Handler
local function HandleDrill(source, data, cb)
    local playerData = FrameworkCompat.GetPlayerData(source)
    if playerData then
        if (not GlobalState["AntiShitlord"] or os.time() > GlobalState["AntiShitlord"] or GlobalState["MazeBankInProgress"]) 
           and not GlobalState["MazeBank:Secured"] then
            
            -- Check server restrictions (similar to above)
            if GetGameTimer() < MAZEBANK_SERVER_START_WAIT or (GlobalState["RestartLockdown"] and not GlobalState["MazeBankInProgress"]) then
                FrameworkCompat.ShowNotification(source, "You Notice The Door Is Barricaded For A Storm, Maybe Check Back Later", "error")
                return
            elseif (GlobalState["Duty:police"] or 0) < MAZEBANK_REQUIRED_POLICE and not GlobalState["MazeBankInProgress"] then
                FrameworkCompat.ShowNotification(source, "Enhanced Security Measures Enabled, Maybe Check Back Later When Things Feel Safer", "error")
                return
            elseif GlobalState["RobberiesDisabled"] then
                FrameworkCompat.ShowNotification(source, "Temporarily Disabled, Please See City Announcements", "error")
                return
            end

            if not _mbInUse.drillPoints[data] then
                _mbInUse.drillPoints[data] = source
                GlobalState["MazeBankInProgress"] = true

                if FrameworkCompat.HasItem(source, "drill", 1) then
                    print(string.format("[ROBBERY] %s %s (%s) Started Drilling Vault Wall: %s",
                        playerData.firstName, playerData.lastName, playerData.sid, data))
                    
                    -- Trigger drilling minigame
                    TriggerClientEvent("Robbery:Client:StartDrillingGame", source, {
                        config = { duration = 45000, difficulty = 3 },
                        data = { wallId = data },
                    })
                    
                    -- Register one-time event for drill result
                    local drillEventName = "Robbery:Server:DrillResult:" .. source .. ":" .. data
                    RegisterNetEvent(drillEventName, function(success)
                        -- Handle item durability
                        if not success or math.random(1, 100) <= 15 then -- 15% chance to break drill
                            FrameworkCompat.RemoveItem(source, "drill", 1)
                        end
                        
                        if success then
                            print(string.format("[ROBBERY] %s %s (%s) Successfully Drilled Vault Box: %s",
                                playerData.firstName, playerData.lastName, playerData.sid, data))
                            
                            if not GlobalState["AntiShitlord"] or os.time() >= GlobalState["AntiShitlord"] then
                                GlobalState["AntiShitlord"] = os.time() + (60 * math.random(10, 15))
                            end

                            _mbGlobalReset = os.time() + MAZEBANK_RESET_TIME
                            
                            -- Give loot
                            local lootTable = {
                                { 60, { name = "moneyroll", min = 420, max = 500 } },
                                { 33, { name = "moneyband", min = 42, max = 50 } },
                                { 5, { name = "valuegoods", min = 26, max = 34 } },
                                { 2, { name = "moneybag", min = 1, max = 1 } },
                            }
                            
                            local randomLoot = lootTable[math.random(#lootTable)]
                            local amount = math.random(randomLoot[2].min, randomLoot[2].max)
                            FrameworkCompat.AddItem(source, randomLoot[2].name, amount)
                            
                            -- Chance for purple dongle
                            if not _purpDongie and math.random(100) <= 10 then
                                _purpDongie = source
                                FrameworkCompat.AddItem(source, "purple_dongle", 1)
                            end

                            GlobalState[string.format("MazeBank:Vault:Wall:%s", data)] = _mbGlobalReset
                            GlobalState["Fleeca:Disable:mazebank_baycity"] = true
                        end
                        
                        _mbInUse.drillPoints[data] = false
                        RemoveEventHandler(drillEventName)
                    end)
                else
                    _mbInUse.drillPoints[data] = false
                    FrameworkCompat.ShowNotification(source, "You don't have a drill", "error")
                end
            else
                FrameworkCompat.ShowNotification(source, "Someone Is Already Interacting With This", "error")
            end
        else
            FrameworkCompat.ShowNotification(source, "Temporary Emergency Systems Enabled, Check Back In A Bit", "error")
        end
    end
end

-- PC Hack Handler
local function HandlePCHack(source, data, cb)
    local playerData = FrameworkCompat.GetPlayerData(source)
    if playerData then
        if (not GlobalState["AntiShitlord"] or os.time() > GlobalState["AntiShitlord"] or GlobalState["MazeBankInProgress"]) 
           and not GlobalState["MazeBank:Secured"] then
            
            -- Check server restrictions (similar to above)
            if GetGameTimer() < MAZEBANK_SERVER_START_WAIT or (GlobalState["RestartLockdown"] and not GlobalState["MazeBankInProgress"]) then
                FrameworkCompat.ShowNotification(source, "Network Offline For A Storm, Check Back Later", "error")
                return
            elseif (GlobalState["Duty:police"] or 0) < MAZEBANK_REQUIRED_POLICE and not GlobalState["MazeBankInProgress"] then
                FrameworkCompat.ShowNotification(source, "Enhanced Security Measures Enabled, Maybe Check Back Later When Things Feel Safer", "error")
                return
            elseif GlobalState["RobberiesDisabled"] then
                FrameworkCompat.ShowNotification(source, "Temporarily Disabled, Please See City Announcements", "error")
                return
            end

            if not _mbInUse.officePcs[data.id] then
                _mbInUse.officePcs[data.id] = source
                GlobalState["MazeBankInProgress"] = true

                if FrameworkCompat.HasItem(source, "adv_electronics_kit", 1) then
                    print(string.format("[ROBBERY] %s %s (%s) Started Hacking Office PC: %s",
                        playerData.firstName, playerData.lastName, playerData.sid, data.id))
                    
                    -- Trigger PC hacking minigame
                    TriggerClientEvent("Robbery:Client:StartPCHackingGame", source, {
                        config = { difficulty = 5, chances = 4 },
                        data = data,
                    })
                    
                    -- Register one-time event for PC hack result
                    local pcHackEventName = "Robbery:Server:PCHackResult:" .. source .. ":" .. data.id
                    RegisterNetEvent(pcHackEventName, function(success)
                        -- Handle item durability
                        if not success or math.random(1, 100) <= 20 then -- 20% chance to break kit
                            FrameworkCompat.RemoveItem(source, "adv_electronics_kit", 1)
                        end
                        
                        if success then
                            print(string.format("[ROBBERY] %s %s (%s) Successfully Hacked Office PC: %s",
                                playerData.firstName, playerData.lastName, playerData.sid, data.id))
                            
                            _mbGlobalReset = os.time() + MAZEBANK_RESET_TIME
                            GlobalState["Fleeca:Disable:mazebank_baycity"] = true
                            GlobalState[string.format("MazeBank:Offices:PC:%s", data.id)] = _mbGlobalReset
                            
                            -- Give crypto rewards
                            FrameworkCompat.AddItem(source, "crypto_voucher", 1, {
                                CryptoCoin = "MALD",
                                Quantity = math.random(120, 200),
                            })

                            -- Chance for HEIST coin
                            if math.random(100) <= (33 * _officesLooted) and not _heistCoin then
                                _heistCoin = true
                                FrameworkCompat.AddItem(source, "crypto_voucher", 1, {
                                    CryptoCoin = "HEIST",
                                    Quantity = 6,
                                })
                            else 
                                _officesLooted = _officesLooted + 1
                            end
                        end
                        
                        _mbInUse.officePcs[data.id] = false
                        RemoveEventHandler(pcHackEventName)
                    end)
                else
                    _mbInUse.officePcs[data.id] = false
                    FrameworkCompat.ShowNotification(source, "You don't have an advanced electronics kit", "error")
                end
            else
                FrameworkCompat.ShowNotification(source, "Someone Is Already Interacting With This", "error")
            end
        else
            FrameworkCompat.ShowNotification(source, "Temporary Emergency Systems Enabled, Check Back In A Bit", "error")
        end
    end
end

-- Register all callbacks based on framework
function RegisterFrameworkCallbacks()
    if Framework.Name == 'ESX' then
        Framework.Object.RegisterServerCallback('Robbery:MazeBank:ElectricBox:Thermite', HandleThermite)
        Framework.Object.RegisterServerCallback('Robbery:MazeBank:Drill', HandleDrill)
        Framework.Object.RegisterServerCallback('Robbery:MazeBank:PC:Hack', HandlePCHack)
    elseif Framework.Name == 'OX' then
        exports.ox_core:registerCallback('Robbery:MazeBank:ElectricBox:Thermite', HandleThermite)
        exports.ox_core:registerCallback('Robbery:MazeBank:Drill', HandleDrill)
        exports.ox_core:registerCallback('Robbery:MazeBank:PC:Hack', HandlePCHack)
    else
        -- Fallback for custom framework
        RegisterNetEvent('Robbery:MazeBank:ElectricBox:Thermite', function(data) HandleThermite(source, data, function() end) end)
        RegisterNetEvent('Robbery:MazeBank:Drill', function(data) HandleDrill(source, data, function() end) end)
        RegisterNetEvent('Robbery:MazeBank:PC:Hack', function(data) HandlePCHack(source, data, function() end) end)
    end
end
