# Maze Bank Robbery - ESX & OX Framework Compatible

This resource has been updated to work with both ESX and OX Core frameworks.

## Framework Support

- **ESX Legacy** - Full support with automatic detection
- **OX Core** - Full support with automatic detection  
- **Custom Framework** - Fallback support for existing custom systems

## Installation

1. **Place the resource** in your `resources` folder
2. **Add to server.cfg**:
   ```
   ensure r-mazerobbery
   ```

## Dependencies

### For ESX Framework:
- `es_extended` (ESX Legacy)
- `esx_hacking` (optional - for better hacking minigame)
- `progressBars` (optional - for progress bars)

### For OX Framework:
- `ox_core`
- `ox_lib` (for notifications and skill checks)
- `ox_inventory`

### Common Dependencies:
- A targeting system (`ox_target`, `qb-target`, or similar)
- A polyzone system for area detection
- Door management system
- CCTV system (optional)
- Particle effects system (optional)
- Sound system (optional)

## Features

### Framework Compatibility
- **Automatic Framework Detection** - Detects ESX or OX on startup
- **Unified Player Data** - Works with both framework's player systems
- **Inventory Integration** - Compatible with both ESX and OX inventory systems
- **Job System** - Works with both framework's job systems
- **Notification System** - Uses appropriate notifications for each framework

### Robbery Features
- **Power Box Hacking** - Disable security systems
- **Thermite Usage** - Alternative power disruption method
- **Vault Drilling** - Access secure areas
- **Office PC Hacking** - Steal cryptocurrency
- **Police Securing** - Law enforcement can secure the bank

## Configuration

The resource automatically detects your framework. No manual configuration needed.

### Custom Framework Support
If you're using a custom framework, the resource will fall back to the original custom system calls.

## Hacking Minigames

### ESX
- Uses `esx_hacking` if available
- Falls back to simple RNG if not available

### OX
- Uses `ox_lib` skill checks
- Falls back to simple RNG if not available

## Notifications

### ESX
- Uses `esx:showNotification`

### OX  
- Uses `ox_lib:notify` with enhanced styling

## Progress Bars

### ESX
- Uses `progressBars` export if available
- Falls back to timer-based progress

### OX
- Uses `ox_lib:progressBar`
- Falls back to timer-based progress

## Troubleshooting

### Framework Not Detected
If you see "No supported framework detected" in console:
1. Ensure ESX or OX Core is started before this resource
2. Check that the framework resource names match exactly
3. Verify framework is properly installed

### Inventory Issues
- **ESX**: Ensure item names match your ESX item database
- **OX**: Ensure `ox_inventory` is running and configured

### Job Permission Issues
- **ESX**: Check job names in your ESX jobs table
- **OX**: Verify group names in OX Core configuration

## Support

This resource maintains backward compatibility with existing custom framework implementations while adding full ESX and OX support.
