fx_version 'cerulean'
game 'gta5'

author 'Maze Bank Robbery Script'
description 'Maze Bank Robbery System with ESX and OX Framework Support'
version '2.0.0'

shared_scripts {
    'shared/config.lua',
    'shared/framework.lua',
    'shared/framework_config.lua',
    'shared/state.lua'
}

client_scripts {
    'client/main.lua',
    'client/threads.lua'
}

server_scripts {
    'server/config.lua',
    'server/main.lua',
    'server/threads.lua',
    'server/items.lua',
    'server/framework_callbacks.lua'
}

dependencies {
    -- ESX Framework (optional)
    -- 'es_extended',

    -- OX Framework (optional)
    -- 'ox_core',
    -- 'ox_lib',
    -- 'ox_inventory',

    -- Required for targeting and zones
    -- 'ox_target', -- or 'qb-target'
    -- 'PolyZone'
}

-- Framework detection
lua54 'yes'
